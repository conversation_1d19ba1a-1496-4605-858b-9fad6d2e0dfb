<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IMPAXX - Transform Your Digital Future</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" href="/icons/apple-icon.png">
    <link rel="manifest" href="/manifest.json">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow-x: hidden;
            background: linear-gradient(135deg, #0f0f14 0%, #12141a 25%, #1a1d26 50%, #1e212a 75%, #242832 100%);
            cursor: none;
            color: #fff;
            line-height: 1.6;
        }

        /* Enhanced custom cursor with controlled movement */
        .cursor {
            position: fixed;
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, rgba(168, 162, 158, 0.9) 0%, rgba(168, 162, 158, 0.4) 40%, transparent 70%);
            border: 1px solid rgba(168, 162, 158, 0.7);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            transition: all 0.08s ease-out;
            box-shadow: 0 0 15px rgba(168, 162, 158, 0.3), inset 0 0 8px rgba(168, 162, 158, 0.2);
        }

        .cursor.active {
            transform: scale(1.3);
            background: radial-gradient(circle, rgba(168, 162, 158, 1) 0%, rgba(168, 162, 158, 0.6) 40%, transparent 70%);
            border-color: rgba(168, 162, 158, 0.9);
            box-shadow: 0 0 20px rgba(168, 162, 158, 0.4), inset 0 0 12px rgba(168, 162, 158, 0.3);
        }

        /* Navigation */
        .nav {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 1rem 0;
            transition: all 0.3s ease;
        }

        .nav::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, 
                rgba(139, 92, 246, 0.1) 0%, 
                rgba(6, 182, 212, 0.1) 50%, 
                rgba(139, 92, 246, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .nav.scrolled {
            background: rgba(0, 0, 0, 0.95);
            padding: 0.5rem 0;
        }

        .nav.scrolled::before {
            opacity: 1;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 800;
            letter-spacing: 0.1em;
            text-transform: uppercase;
            color: #fff !important;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            text-decoration: none;
        }

        .logo::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.4));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .logo:hover::after {
            transform: scaleX(1);
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-links a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            position: relative;
        }

        .nav-links a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .nav-links a:hover::before {
            opacity: 1;
        }

        .nav-links a:hover {
            color: #fff;
            transform: translateY(-2px);
        }

        /* Mobile menu button */
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
        }

        .hero-section {
            position: relative;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 8rem 2rem 4rem;
            background: 
                linear-gradient(135deg, rgba(26, 29, 38, 0.8) 0%, rgba(30, 33, 42, 0.9) 50%, rgba(36, 40, 50, 0.8) 100%),
                radial-gradient(ellipse at 20% 30%, rgba(45, 55, 72, 0.1) 0%, transparent 50%),
                radial-gradient(ellipse at 80% 70%, rgba(55, 65, 81, 0.1) 0%, transparent 50%),
                linear-gradient(180deg, #1a1d26 0%, #242832 100%);
            overflow: hidden;
        }

        /* Interactive particle system that responds to presence */
        .neural-network {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .neural-node {
            position: absolute;
            width: 3px;
            height: 3px;
            background: rgba(168, 162, 158, 0.7);
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 0 8px rgba(168, 162, 158, 0.3);
        }

        .node-1 { top: 18%; left: 12%; }
        .node-2 { top: 25%; right: 20%; }
        .node-3 { bottom: 30%; left: 18%; }
        .node-4 { top: 55%; right: 12%; }
        .node-5 { bottom: 20%; right: 25%; }
        .node-6 { top: 40%; left: 25%; }
        .node-7 { top: 70%; left: 40%; }

        /* Dynamic connections that form as you watch */
        .neural-connection {
            position: absolute;
            height: 1px;
            background: linear-gradient(90deg, 
                rgba(168, 162, 158, 0), 
                rgba(168, 162, 158, 0.4), 
                rgba(168, 162, 158, 0));
            transform-origin: left center;
            opacity: 0;
            animation: connectionAwaken 8s ease-in-out infinite;
        }

        .connection-1 {
            top: 18%;
            left: 12%;
            width: 180px;
            transform: rotate(20deg);
            animation-delay: 1s;
        }

        .connection-2 {
            top: 55%;
            right: 25%;
            width: 200px;
            transform: rotate(-35deg);
            animation-delay: 3s;
        }

        .connection-3 {
            bottom: 30%;
            left: 18%;
            width: 150px;
            transform: rotate(45deg);
            animation-delay: 5s;
        }

        @keyframes connectionAwaken {
            0%, 20% { opacity: 0; transform: scaleX(0) rotate(var(--rotation)); }
            30%, 80% { opacity: 0.8; transform: scaleX(1) rotate(var(--rotation)); }
            100% { opacity: 0; transform: scaleX(0) rotate(var(--rotation)); }
        }

        /* Scanning revelation system */
        .revelation-scanner {
            position: absolute;
            top: 0;
            left: -100px;
            width: 2px;
            height: 100%;
            background: linear-gradient(to bottom, 
                transparent,
                rgba(168, 162, 158, 0.8),
                transparent);
            animation: scanReveal 12s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes scanReveal {
            0% { left: -100px; opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { left: calc(100% + 100px); opacity: 0; }
        }

        /* Hidden elements revealed by scanner */
        .hidden-insight {
            position: absolute;
            font-size: 0.7rem;
            color: rgba(168, 162, 158, 0);
            font-weight: 300;
            transition: color 0.3s ease;
            pointer-events: none;
        }

        .insight-1 {
            top: 20%;
            left: 20%;
            animation: insightReveal 12s infinite;
            animation-delay: 2s;
        }

        .insight-2 {
            bottom: 25%;
            right: 20%;
            animation: insightReveal 12s infinite;
            animation-delay: 6s;
        }

        @keyframes insightReveal {
            0%, 15% { color: rgba(168, 162, 158, 0); }
            20%, 25% { color: rgba(168, 162, 158, 0.6); }
            30%, 85% { color: rgba(168, 162, 158, 0); }
        }

        /* Enhanced precision mechanics */
        .precision-apparatus {
            position: absolute;
            border: 1px solid rgba(168, 162, 158, 0.12);
            border-radius: 50%;
        }

        .apparatus-outer {
            top: 10%;
            right: 8%;
            width: 120px;
            height: 120px;
            animation: precisionRotate 30s linear infinite;
        }

        .apparatus-inner {
            top: 12%;
            right: 10%;
            width: 80px;
            height: 80px;
            animation: precisionRotate 20s linear infinite reverse;
            border-style: dashed;
        }

        .apparatus-core {
            bottom: 15%;
            left: 8%;
            width: 60px;
            height: 60px;
            animation: precisionRotate 25s linear infinite;
            opacity: 0.6;
        }

        @keyframes precisionRotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Advanced typography with micro-interactions */
        .hero-logo {
            font-size: clamp(3.8rem, 8vw, 7rem);
            font-weight: 800;
            letter-spacing: 0.12em;
            margin-bottom: 1rem;
            color: #f8f9fa;
            position: relative;
            opacity: 0;
            animation: logoMaterialize 1.5s cubic-bezier(0.4, 0, 0.2, 1) 0.5s forwards;
            text-shadow: 0 0 40px rgba(168, 162, 158, 0.1);
        }

        .hero-logo::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -20px;
            width: calc(100% + 40px);
            height: calc(100% + 20px);
            background: radial-gradient(ellipse at center, 
                rgba(168, 162, 158, 0.03) 0%, 
                transparent 70%);
            animation: energyField 4s ease-in-out 2s infinite;
            border-radius: 10px;
        }

        @keyframes logoMaterialize {
            0% { 
                opacity: 0; 
                transform: translateY(40px) scale(0.9);
                filter: blur(10px);
            }
            100% { 
                opacity: 1; 
                transform: translateY(0) scale(1);
                filter: blur(0);
            }
        }

        @keyframes energyField {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.02); }
        }

        .main-heading {
            font-size: clamp(3rem, 6.5vw, 5.2rem);
            font-weight: 200;
            margin-bottom: 2.5rem;
            color: #f1f3f4;
            opacity: 0;
            animation: headingEmerge 1.2s ease-out 1.5s forwards;
        }

        .word-osez {
            font-weight: 700;
            position: relative;
            display: inline-block;
        }

        .word-osez::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(168, 162, 158, 0.15), transparent);
            transform: translateX(-100%);
            animation: emphasis 3s ease-in-out 3s infinite;
        }

        .word-transformer {
            position: relative;
            font-weight: 100;
            letter-spacing: 0.08em;
        }

        @keyframes headingEmerge {
            from { opacity: 0; transform: translateY(30px); filter: blur(5px); }
            to { opacity: 1; transform: translateY(0); filter: blur(0); }
        }

        @keyframes emphasis {
            0%, 100% { transform: translateX(-40%); }
            50% { transform: translateX(40%); }
        }

        /* Enhanced value proposition with emotional impact */
        .strategic-brief {
            max-width: 900px;
            margin: 0 auto 4rem;
            opacity: 0;
            animation: briefingReveal 1.2s ease-out 2.5s forwards;
        }

        .mission-statement {
            font-size: clamp(1.4rem, 3vw, 1.8rem);
            color: rgba(255, 255, 255, 0.95);
            font-weight: 400;
            margin-bottom: 1.5rem;
            line-height: 1.4;
            position: relative;
        }

        .tactical-approach {
            font-size: clamp(1.1rem, 2.3vw, 1.3rem);
            color: rgba(255, 255, 255, 0.75);
            font-weight: 300;
            line-height: 1.7;
            max-width: 800px;
            margin: 0 auto;
        }

        .power-phrase {
            color: #ffffff;
            font-weight: 600;
            position: relative;
            padding: 0 4px;
        }

        .power-phrase::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, 
                transparent, 
                rgba(168, 162, 158, 0.6), 
                transparent);
            animation: powerGlow 3s ease-in-out 4s infinite;
        }

        @keyframes briefingReveal {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes powerGlow {
            0%, 100% { opacity: 0.4; transform: scaleX(0.5); }
            50% { opacity: 1; transform: scaleX(1.2); }
        }

        /* Mission-critical CTA */
        .mission-cta {
            position: relative;
            display: inline-flex;
            align-items: center;
            padding: 20px 42px;
            font-size: 1.2rem;
            font-weight: 500;
            color: #000000;
            text-decoration: none;
            background: #ffffff;
            border-radius: 4px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            opacity: 0;
            animation: missionLaunch 1.2s ease-out 3.5s forwards;
            overflow: hidden;
            border: 1px solid transparent;
        }

        .mission-cta::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, 
                rgba(255, 255, 255, 0.1), 
                rgba(255, 255, 255, 0.05));
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .mission-cta::after {
            content: '→';
            margin-left: 10px;
            transition: all 0.3s ease;
            font-weight: 200;
            font-size: 1.3em;
        }

        .mission-cta:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 15px 45px rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.1);
        }

        .mission-cta:hover::before {
            transform: translateX(100%);
        }

        .mission-cta:hover::after {
            transform: translateX(6px) scale(1.1);
        }

        @keyframes missionLaunch {
            from { opacity: 0; transform: translateY(15px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Enhanced ambient intelligence */
        .intelligence-grid {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.03;
            background-image: 
                radial-gradient(circle at 2px 2px, rgba(168, 162, 158, 0.8) 1px, transparent 0);
            background-size: 80px 80px;
            animation: intelligenceShift 20s ease-in-out infinite;
        }

        @keyframes intelligenceShift {
            0%, 100% { 
                transform: translate(0, 0) rotate(0deg); 
                opacity: 0.03; 
            }
            25% { 
                transform: translate(20px, -10px) rotate(0.5deg); 
                opacity: 0.04; 
            }
            50% { 
                transform: translate(40px, 20px) rotate(0deg); 
                opacity: 0.035; 
            }
            75% { 
                transform: translate(-10px, 30px) rotate(-0.5deg); 
                opacity: 0.045; 
            }
        }

        .content-wrapper {
            position: relative;
            z-index: 2;
        }

        /* Section Foundations */
        .section-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 8rem 2rem;
        }

        .section-header {
            text-align: center;
            margin-bottom: 6rem;
            position: relative;
        }

        .section-label {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.5);
            font-weight: 300;
            letter-spacing: 0.15em;
            text-transform: uppercase;
            display: block;
            margin-bottom: 1rem;
        }

        .section-title {
            font-size: clamp(2.5rem, 5vw, 4rem);
            color: #ffffff;
            font-weight: 300;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .accent-word {
            font-weight: 700;
            position: relative;
        }

        .accent-word::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(168, 162, 158, 0.6), transparent);
            opacity: 0.7;
        }

        .section-subtitle {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 300;
            max-width: 600px;
            margin: 0 auto;
        }

        /* About Section - Enhanced with integrated approach */
        .about-section {
            background: 
                linear-gradient(135deg, rgba(26, 29, 38, 0.95) 0%, rgba(30, 33, 42, 0.98) 50%, rgba(36, 40, 50, 0.95) 100%),
                radial-gradient(ellipse at 10% 20%, rgba(45, 55, 72, 0.08) 0%, transparent 60%),
                radial-gradient(ellipse at 90% 80%, rgba(55, 65, 81, 0.08) 0%, transparent 60%),
                linear-gradient(180deg, #1a1d26 0%, #242832 100%);
            position: relative;
        }

        .section-grid {
            position: relative;
        }

        .grid-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(168, 162, 158, 0.02) 1px, transparent 1px),
                linear-gradient(90deg, rgba(168, 162, 158, 0.02) 1px, transparent 1px);
            background-size: 60px 60px;
            opacity: 0.3;
        }

        .about-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: start;
            margin-bottom: 8rem;
        }

        .philosophy-block,
        .approach-block {
            position: relative;
        }

        .philosophy-title,
        .approach-title {
            font-size: 1.5rem;
            color: #ffffff;
            font-weight: 600;
            margin-bottom: 1.5rem;
        }

        .philosophy-text {
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.7;
            margin-bottom: 3rem;
        }

        .insight-metrics {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-top: 30px;
        }

        .insight-metrics .metric-card:nth-child(4),
        .insight-metrics .metric-card:nth-child(5) {
            grid-column: span 1;
            justify-self: center;
        }

        .metric-card {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            padding: 16px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.02); 
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            transition: transform 0.3s ease, background 0.3s ease;
        }

        .metric-icon {
            font-size: 1.8rem;
            margin-bottom: 6px;
            opacity: 0.9;
        }

        .metric-number {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 4px;
            color: #fff;
        }

        .metric-label {
            font-size: 0.85rem;
            color: #bbb;
            line-height: 1.2;
        }

        .approach-steps {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .step-item {
            display: flex;
            align-items: flex-start;
            gap: 1.5rem;
            padding: 1.5rem;
            background: rgba(168, 162, 158, 0.02);
            border: 1px solid rgba(168, 162, 158, 0.05);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .step-item:hover {
            background: rgba(168, 162, 158, 0.04);
            border-color: rgba(168, 162, 158, 0.1);
            transform: translateY(-2px);
        }

        .step-icon {
            font-size: 1.5rem;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(168, 162, 158, 0.08);
            border-radius: 50%;
            flex-shrink: 0;
        }

        .step-content h4 {
            color: #ffffff;
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }

        .step-content p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.95rem;
            line-height: 1.5;
        }

        /* Interactive Method Section within About */
        .interactive-approach {
            margin-top: 6rem;
            padding-top: 6rem;
            border-top: 1px solid rgba(168, 162, 158, 0.1);
        }

        .approach-subtitle {
            text-align: center;
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 300;
            max-width: 800px;
            margin: 0 auto 4rem;
            line-height: 1.6;
        }

        .method-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
            margin-bottom: 4rem;
        }

        .method-card {
			background: rgba(168, 162, 158, 0.02) !important;
			border: 1px solid rgba(168, 162, 158, 0.05) !important;
		}

		.method-card:hover {
			background: rgba(168, 162, 158, 0.04) !important;
			border-color: rgba(168, 162, 158, 0.1) !important;
		}

		/* Remove the purple bar */
		.method-card::before {
			background: linear-gradient(90deg, rgba(168, 162, 158, 0.2), rgba(168, 162, 158, 0.2)) !important;
		}
		
		.method-card:hover::before {
			opacity: 1;
		}
        .card-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            display: block;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #fff;
        }

        .card-preview {
            color: #aaa;
            font-size: 0.95rem;
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        .expand-hint {
            color: #666;
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .expand-hint::after {
            color: rgba(168, 162, 158, 0.6) !important;
			content: '📃';
            transition: transform 0.3s ease;
        }

        .method-card:hover .expand-hint::after {
            transform: translateX(4px);
        }

       .philosophy-section {
			background: rgba(168, 162, 158, 0.03) !important;
			border: 1px solid rgba(168, 162, 158, 0.08) !important;
		}

        .philosophy-section h3 {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #fff;
        }

        .philosophy-text-approach {
            font-size: 1.1rem;
            color: #ccc;
            max-width: 700px;
            margin: 0 auto 2rem;
            line-height: 1.6;
        }

        .cta-section {
            text-align: center;
            margin-top: 3rem;
        }

        .cta-button {
			background: linear-gradient(135deg, #ffffff, #e5e7eb) !important;
			color: #000 !important;
			display: inline-flex;
			align-items: center;
			gap: 0.5rem;
			background: linear-gradient(135deg, #ffffff, #e5e7eb);
			color: #000;
			padding: 1rem 2rem;
			border-radius: 50px;
			text-decoration: none;
			font-weight: 600;
			font-size: 1.1rem;
			transition: all 0.3s ease;
			border: none;
			cursor: pointer;
		}

		.cta-button:hover {
			transform: translateY(-2px);
			box-shadow: 0 10px 25px rgba(255, 255, 255, 0.2);
		}

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.3s ease;
        }

        .modal-content {
            background: #111;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 2rem;
            max-width: 90vw;
            max-height: 90vh;
            width: 600px;
            overflow-y: auto;
            position: relative;
            animation: slideIn 0.3s ease;
        }

        .modal-close {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: none;
            border: none;
            color: #888;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
        }

        .modal h3 {
            color: #fff;
            font-size: 1.8rem;
            margin-bottom: 1rem;
        }

        .modal p {
            color: #ccc;
            margin-bottom: 1rem;
            line-height: 1.7;
        }

        .modal .highlight {
            background: rgba(79, 70, 229, 0.2);
            color: #fff;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        /* Services Section */
        .services-section {
            position: relative;
            overflow: hidden;
            background: 
                linear-gradient(135deg, #1e212a 0%, #242832 40%, #1a1d26 100%),
                radial-gradient(circle at 30% 40%, rgba(108, 99, 255, 0.05), transparent 70%),
                radial-gradient(circle at 70% 60%, rgba(255, 99, 221, 0.05), transparent 70%);
        }

        .services-section::before {
            content: "";
            position: absolute;
            inset: 0;
            background:
                radial-gradient(circle at 30% 20%, rgba(108, 99, 255, 0.04), transparent 70%),
                radial-gradient(circle at 70% 80%, rgba(255, 99, 221, 0.04), transparent 70%);
            z-index: 0;
            pointer-events: none;
            mix-blend-mode: soft-light;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px 60px;
            margin-top: 40px;
        }

        .service-card {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 14px;
            padding: 28px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            box-shadow: 0 6px 18px rgba(0, 0, 0, 0.25);
            transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
        }

        .service-card:hover {
            transform: translateY(-4px);
            background: rgba(255, 255, 255, 0.06);
            box-shadow: 0 8px 24px rgba(168, 162, 158, 0.15);
        }

        .service-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }

        .service-icon {
            font-size: 2.4rem;
            transition: transform 0.3s ease;
        }

        .service-card:hover .service-icon {
            transform: scale(1.1);
        }

        .service-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #fff;
        }

        .service-description {
            font-size: 1rem;
            color: #bbb;
            line-height: 1.4;
            margin-bottom: 16px;
        }

        .service-features {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .service-features span {
            background: rgba(255, 255, 255, 0.05);
            padding: 4px 10px;
            border-radius: 6px;
            font-size: 0.8rem;
            color: #ccc;
        }

		/* Image card style (so it matches the size of other cards) */
		.service-card.image-card {
		  padding: 0;
		  background: none;
		}

		.service-card.image-card img {
		  display: block;
		  width: 100%;
		  height: 300px;
		  aspect-ratio: 3 / 2;     /* Forces 1200x800 ratio */
		  object-fit: cover;
		  border-radius: 10px;
		  filter: brightness(0.6) contrast(1.1) saturate(0.8);
		  transition: filter 0.3s ease;
		}

		.service-card.image-card img:hover {
		filter: brightness(0.8) contrast(1.1) saturate(1);
		}


        /* Contact Section */
        .contact-section {
            background: 
                linear-gradient(135deg, rgba(36, 40, 50, 0.95) 0%, rgba(42, 48, 60, 0.98) 50%, rgba(30, 33, 42, 0.95) 100%),
                radial-gradient(ellipse at 20% 30%, rgba(65, 75, 91, 0.08) 0%, transparent 60%),
                radial-gradient(ellipse at 80% 70%, rgba(75, 85, 101, 0.08) 0%, transparent 60%),
                linear-gradient(180deg, #2a303e 0%, #1a1d26 100%);
            position: relative;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 6rem;
            align-items: start;
        }

        .contact-info {
            position: sticky;
            top: 2rem;
        }

        .contact-description {
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            margin: 2rem 0 3rem 0;
        }

        .contact-details {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .detail-label {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.5);
            font-weight: 300;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .detail-link {
            color: #ffffff;
            text-decoration: none;
            font-size: 1.2rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .detail-link:hover {
            color: rgba(255, 255, 255, 0.8);
            transform: translateX(4px);
        }

        .detail-text {
            color: #ffffff;
            font-size: 1.2rem;
            font-weight: 500;
        }

        /* Contact Form */
        .consultation-form {
            background: rgba(168, 162, 158, 0.02);
            border: 1px solid rgba(168, 162, 158, 0.08);
            border-radius: 12px;
            padding: 2.5rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .form-field {
            margin-bottom: 1.5rem;
        }

        .form-field label {
            display: block;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-field input,
        .form-field textarea {
            width: 100%;
            padding: 1rem;
            background: rgba(168, 162, 158, 0.05);
            border: 1px solid rgba(168, 162, 158, 0.1);
            border-radius: 6px;
            color: #ffffff;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-field input:focus,
        .form-field textarea:focus {
            outline: none;
            border-color: rgba(168, 162, 158, 0.3);
            background: rgba(168, 162, 158, 0.08);
        }

        .form-field textarea {
            resize: vertical;
            min-height: 120px;
        }

        .submit-btn {
            width: 100%;
            padding: 1.2rem;
            background: #ffffff;
            color: #000000;
            border: none;
            border-radius: 6px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .submit-btn:hover {
            background: rgba(255, 255, 255, 0.9);
            transform: translateY(-2px);
        }

        /* Footer */
        .footer {
            padding: 4rem 2rem 2rem;
            text-align: center;
            background: 
                linear-gradient(135deg, rgba(26, 29, 38, 0.98) 0%, rgba(20, 23, 30, 1) 100%),
                linear-gradient(180deg, #1a1d26 0%, #141720 100%);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .footer-links a {
            color: rgba(255, 255, 255, 0.6);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: #8b5cf6;
        }

        .copyright {
            color: rgba(255, 255, 255, 0.4);
            font-size: 0.9rem;
        }

        /* Animations */
        @keyframes hero-fade-in {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.8s ease;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* Enhanced Mobile Responsiveness */
        @media (max-width: 768px) {
            .cursor { display: none; }
            
            .nav-links {
                display: none;
            }
            
            .mobile-menu-btn {
                display: block;
            }
            
            .neural-node,
            .neural-connection,
            .precision-apparatus,
            .hidden-insight {
                display: none;
            }
            
            .hero-section {
                padding: 6rem 1.5rem 4rem;
            }
            
            .section-container {
                padding: 4rem 1.5rem;
            }
            
            .about-content,
            .contact-grid {
                grid-template-columns: 1fr;
                gap: 3rem;
            }
            
            .services-grid {
                grid-template-columns: 1fr;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .strategic-brief {
                margin-bottom: 3rem;
            }
            
            .insight-metrics {
                justify-content: center;
                flex-wrap: wrap;
            }

            .method-grid {
                gap: 1rem;
            }

            .method-card {
                padding: 1.5rem;
            }

            .philosophy-section {
                padding: 2rem 1rem;
                margin: 2rem 0;
            }

            .modal-content {
                padding: 1.5rem;
                width: 95vw;
            }
        }

        /* Mobile Responsiveness Fixes for IMPAXX.CA */

        /* 1. Fix text overflow and horizontal scrolling */
        * {
        box-sizing: border-box;
        }

        body {
        overflow-x: hidden; /* Prevent horizontal scroll */
        margin: 0;
        padding: 0;
        }

        /* 2. Ensure all containers respect viewport width */
        .container, .main-content, .section {
        max-width: 100%;
        padding-left: 15px;
        padding-right: 15px;
        box-sizing: border-box;
        }

        /* 3. Fix card width issues */
        .card, .service-card, .feature-card {
        width: 100% !important;
        max-width: 100%;
        margin: 0 0 20px 0;
        box-sizing: border-box;
        overflow: hidden; /* Prevent content from spilling out */
        }

        /* 4. Mobile-specific layout adjustments */
        @media (max-width: 768px) {
        /* Force single column layout */
        .row, .grid, .flex-container {
            flex-direction: column !important;
            width: 100%;
        }
        
        /* Fix card containers */
        .col, .column, [class*="col-"] {
            width: 100% !important;
            max-width: 100% !important;
            flex: none !important;
            margin-bottom: 20px;
        }
        
        /* Text and content adjustments */
        h1, h2, h3, h4, h5, h6 {
            word-wrap: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
        }
        
        p, div, span {
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
        
        /* Prevent images from overflowing */
        img {
            max-width: 100% !important;
            height: auto !important;
        }
        }

        /* 5. Contact form mobile fixes */
        @media (max-width: 768px) {
        .contact-section, .contact-form-container {
            display: block !important;
            width: 100%;
        }
        
        .contact-form {
            width: 100% !important;
            max-width: 100% !important;
            margin-top: 30px; /* Add space above form */
            position: relative !important; /* Override any absolute positioning */
            z-index: auto !important;
        }
        
        /* If there's a two-column layout with text on left and form on right */
        .contact-text, .contact-info {
            width: 100% !important;
            margin-bottom: 30px;
        }
        
        /* Form inputs mobile styling */
        .contact-form input,
        .contact-form textarea,
        .contact-form select {
            width: 100% !important;
            max-width: 100% !important;
            box-sizing: border-box;
            margin-bottom: 15px;
        }
        }

        /* 6. Additional safety measures */
        @media (max-width: 768px) {
        /* Ensure no element can cause horizontal overflow */
        .section, .container, .wrapper {
            max-width: 100vw;
            overflow-x: hidden;
        }
        
        /* Fix any absolute positioned elements that might overflow */
        [style*="position: absolute"] {
            position: relative !important;
        }
        
        /* Button adjustments */
        .btn, .button, button {
            width: 100%;
            max-width: 300px;
            margin: 10px auto;
            display: block;
        }
        
        /* Navigation fixes if needed */
        .navbar, .nav, .menu {
            flex-wrap: wrap;
        }
        
        /* Table responsiveness */
        table {
            width: 100%;
            display: block;
            overflow-x: auto;
            white-space: nowrap;
        }
        }

        /* 7. Viewport and zoom fixes */
        html {
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
        }

        /* 8. Emergency fixes for stubborn elements */
        @media (max-width: 768px) {
        /* Force any element with fixed width to be responsive */
        [style*="width:"] {
            width: 100% !important;
            max-width: 100% !important;
        }
        
        /* Fix margins that might cause overflow */
        * {
            margin-left: 0 !important;
            margin-right: 0 !important;
        }
        
        .container *, .main-content * {
            margin-left: auto !important;
            margin-right: auto !important;
        }
        }

        /* Additional Mobile Fixes - Add these to the very bottom of your CSS */

        @media (max-width: 768px) {
            /* Fix the right gap/overflow issue */
            html, body {
                max-width: 100vw !important;
                overflow-x: hidden !important;
            }
            
            /* Ensure all main containers respect mobile width */
            .hero-section,
            .about-section,
            .services-section,
            .contact-section,
            .section-container,
            .nav-container {
                max-width: 100vw !important;
                padding-left: 1rem !important;
                padding-right: 1rem !important;
                margin-left: 0 !important;
                margin-right: 0 !important;
            }
            
            /* Fix contact form positioning issue */
            .contact-grid {
                grid-template-columns: 1fr !important;
                gap: 2rem !important;
            }
            
            .contact-info {
                position: static !important; /* Remove sticky positioning on mobile */
                order: 1;
            }
            
            .contact-form {
                order: 2;
                margin-top: 0 !important;
            }
            
            .consultation-form {
                padding: 1.5rem !important;
                margin: 0 !important;
            }
            
            /* Fix service cards width */
            .services-grid {
                grid-template-columns: 1fr !important;
                gap: 1.5rem !important;
            }
            
            .service-card {
                width: 100% !important;
                max-width: 100% !important;
                margin-left: 0 !important;
                margin-right: 0 !important;
            }
            
            /* Fix method grid cards */
            .method-grid {
                grid-template-columns: 1fr !important;
                gap: 1rem !important;
            }
            
            .method-card {
                width: 100% !important;
                max-width: 100% !important;
            }
            
            /* Fix any text that might overflow */
            .hero-logo,
            .main-heading,
            .section-title {
                word-break: break-word !important;
                overflow-wrap: break-word !important;
            }
            
            /* Fix form inputs */
            .form-field input,
            .form-field textarea {
                width: 100% !important;
                max-width: 100% !important;
                box-sizing: border-box !important;
            }
            
            .form-row {
                grid-template-columns: 1fr !important;
                gap: 1rem !important;
            }
            
            /* Fix any remaining width issues */
            * {
                max-width: 100% !important;
            }
            
            /* Specific fix for elements that might cause horizontal scroll */
            .strategic-brief,
            .mission-statement,
            .tactical-approach {
                max-width: 100% !important;
                padding-left: 0 !important;
                padding-right: 0 !important;
            }
            
            /* Navigation adjustments */
            .nav-container {
                padding: 0 1rem !important;
            }
            
            /* Footer fixes */
            .footer {
                padding: 2rem 1rem 1rem !important;
            }
            
            .footer-links {
                flex-direction: column !important;
                gap: 1rem !important;
            }
        }

        /* Extra small screens */
        @media (max-width: 480px) {
            .section-container {
                padding: 3rem 0.75rem !important;
            }
            
            .hero-section {
                padding: 6rem 0.75rem 4rem !important;
            }
            
            .consultation-form {
                padding: 1rem !important;
            }
        }

        /* Final Text Sizing Fixes - Add to the very bottom of your CSS */

        @media (max-width: 768px) {
            /* Fix hero text sizing to prevent line breaks */
            .hero-logo {
                font-size: clamp(2.5rem, 12vw, 4rem) !important;
                line-height: 1.1 !important;
                letter-spacing: 0.08em !important;
                white-space: nowrap !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
            }
            
            .main-heading {
                font-size: clamp(2rem, 10vw, 3.5rem) !important;
                line-height: 1.2 !important;
                white-space: nowrap !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
            }
            
            /* Ensure "Osez transformer" stays on one line */
            .word-osez,
            .word-transformer {
                display: inline !important;
                white-space: nowrap !important;
            }
            
            /* Fix section titles */
            .section-title {
                font-size: clamp(2rem, 8vw, 3rem) !important;
                line-height: 1.2 !important;
                white-space: nowrap !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
            }
            
            /* Fix mission statement and tactical approach */
            .mission-statement {
                font-size: clamp(1.1rem, 4vw, 1.4rem) !important;
                line-height: 1.3 !important;
                padding: 0 0.5rem !important;
            }
            
            .tactical-approach {
                font-size: clamp(0.95rem, 3.5vw, 1.2rem) !important;
                line-height: 1.5 !important;
                padding: 0 0.5rem !important;
            }
            
            /* Better approach for responsive text - remove white-space restrictions for content */
            .strategic-brief p {
                white-space: normal !important;
                word-wrap: break-word !important;
                overflow-wrap: break-word !important;
            }
            
            /* Navigation logo fix */
            .logo {
                font-size: clamp(1.2rem, 6vw, 1.8rem) !important;
                white-space: nowrap !important;
            }
            
            /* Contact section title */
            .contact-section .section-title {
                font-size: clamp(1.8rem, 7vw, 2.5rem) !important;
            }
            
            /* Philosophy and approach titles */
            .philosophy-title,
            .approach-title {
                font-size: clamp(1.2rem, 5vw, 1.5rem) !important;
                line-height: 1.3 !important;
            }
            
            /* Service card titles */
            .service-title {
                font-size: clamp(1.1rem, 4vw, 1.4rem) !important;
                line-height: 1.3 !important;
            }
            
            /* Method card titles */
            .card-title {
                font-size: clamp(1rem, 4vw, 1.3rem) !important;
                line-height: 1.3 !important;
            }
        }

        /* Extra tight screens */
        @media (max-width: 480px) {
            .hero-logo {
                font-size: clamp(2rem, 15vw, 3rem) !important;
                letter-spacing: 0.05em !important;
            }
            
            .main-heading {
                font-size: clamp(1.5rem, 12vw, 2.5rem) !important;
            }
            
            .section-title {
                font-size: clamp(1.5rem, 10vw, 2rem) !important;
            }
        }

        /* Ultra small screens - emergency scaling */
        @media (max-width: 360px) {
            .hero-logo {
                font-size: clamp(1.8rem, 18vw, 2.5rem) !important;
            }
            
            .main-heading {
                font-size: clamp(1.3rem, 15vw, 2rem) !important;
            }
        }

        /* Final fix for philosophy section and approach cards */

        @media (max-width: 768px) {
            /* Fix about content grid to single column */
            .about-content {
                display: block !important;
                grid-template-columns: none !important;
            }
            
            .philosophy-block,
            .approach-block {
                width: 100% !important;
                max-width: 100% !important;
                margin-bottom: 2rem !important;
            }
            
            /* Fix step items in approach */
            .step-item {
                width: 100% !important;
                max-width: 100% !important;
                padding: 1rem !important;
                margin-bottom: 1rem !important;
                box-sizing: border-box !important;
            }
            
            .step-content {
                width: 100% !important;
                max-width: 100% !important;
            }
            
            .step-content h4,
            .step-content p {
                word-wrap: break-word !important;
                overflow-wrap: break-word !important;
            }
            
            /* Fix philosophy text */
            .philosophy-text {
                width: 100% !important;
                max-width: 100% !important;
                word-wrap: break-word !important;
                overflow-wrap: break-word !important;
                padding: 0 !important;
                margin: 0 0 1rem 0 !important;
            }
            
            /* Fix metrics cards container */
            .insight-metrics {
                display: grid !important;
                grid-template-columns: repeat(auto-fit, minmax(80px, 1fr)) !important;
                gap: 0.5rem !important;
                width: 100% !important;
                max-width: 100% !important;
            }
            
            .metric-card {
                width: 100% !important;
                max-width: 100% !important;
                padding: 0.5rem !important;
                margin: 0 !important;
            }
        }

        @media (max-width: 480px) {
            .mission-cta {
                padding: 18px 32px;
                font-size: 1.1rem;
            }
            
            .section-container {
                padding: 3rem 1rem;
            }
            
            .section-header {
                margin-bottom: 3rem;
            }
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Performance optimizations */
        .service-card,
        .mission-cta {
            will-change: transform;
        }

        /* Reduced motion for accessibility */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
    </style>
</head>
<body>
    <div class="cursor"></div>
    
    <!-- Navigation -->
    <nav class="nav">
        <div class="nav-container">
            <a href="/" class="logo">IMPAXX</a>
            <ul class="nav-links">
                <li><a href="#about">À propos</a></li>
                <li><a href="#services">Services</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
        </div>
    </nav>
    
    <!-- Hero Section -->
    <section class="hero-section" id="hero">
        <!-- Intelligence grid background -->
        <div class="intelligence-grid"></div>
        
        <!-- Revelation scanner -->
        <div class="revelation-scanner"></div>
        
        <!-- Hidden insights revealed by scanner -->
        <div class="hidden-insight insight-1">pattern detected</div>
        <div class="hidden-insight insight-2">opportunity identified</div>
        
        <!-- Neural network system -->
        <div class="neural-network">
            <div class="neural-node node-1"></div>
            <div class="neural-node node-2"></div>
            <div class="neural-node node-3"></div>
            <div class="neural-node node-4"></div>
            <div class="neural-node node-5"></div>
            <div class="neural-node node-6"></div>
            <div class="neural-node node-7"></div>
            
            <!-- Dynamic connections -->
            <div class="neural-connection connection-1" style="--rotation: 20deg;"></div>
            <div class="neural-connection connection-2" style="--rotation: -35deg;"></div>
            <div class="neural-connection connection-3" style="--rotation: 45deg;"></div>
        </div>
        
        <!-- Precision apparatus -->
        <div class="precision-apparatus apparatus-outer"></div>
        <div class="precision-apparatus apparatus-inner"></div>
        <div class="precision-apparatus apparatus-core"></div>
        
        <div class="content-wrapper">
            <h1 class="hero-logo">IMPAXX</h1>
            
            <h2 class="main-heading">
                <span class="word-osez">Osez</span> 
                <span class="word-transformer">transformer</span>
            </h2>
            
            <div class="strategic-brief">
                <p class="mission-statement">Solutions numériques audacieuses pour PME ambitieuses.</p>
                <p class="tactical-approach">De la stratégie à l'exécution, IMPAXX vous guide vers une <span class="power-phrase">transformation digitale</span> efficace, humaine et durable.</p>
            </div>
            
            <a href="#contact" class="mission-cta">Démarrons votre projet</a>
        </div>
    </section>

    <!-- About Section with Integrated Approach -->
    <section class="about-section" id="about">
        <div class="section-container">
            <div class="section-grid">
                <div class="grid-background"></div>
                
                <div class="section-header">
                    <span class="section-label">01 — À propos</span>
                    <h2 class="section-title">À propos <span class="accent-word">d'IMPAXX</span></h2>
                </div>
                
                <div class="about-content fade-in">
                    <!-- Left Column -->
                    <div class="philosophy-block">
                        <h3 class="philosophy-title">Notre philosophie</h3>
                        <p class="philosophy-text">
                            Fondée par <strong>Jeff Blain</strong>, expert en technologies avec plus de 25 ans d'expérience,
                            <strong>IMPAXX</strong> est née d'une vision claire : rendre accessible aux PME l'expertise technologique souvent réservée aux grandes entreprises.
                        </p>
                        <p class="philosophy-text">
                            Notre approche se distingue par notre <strong>audace</strong>, notre <strong>créativité</strong> et notre engagement à <strong>penser différemment</strong>.
                        </p>

                        <!-- Metrics -->
                        <div class="insight-metrics">
                            <div class="metric-card">
                                <div class="metric-icon">🏆</div>
                                <div class="metric-number">25+</div>
                                <div class="metric-label">Années d'expérience</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-icon">🚀</div>
                                <div class="metric-number">30+</div>
                                <div class="metric-label">Projets stratégiques</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-icon">🌍</div>
                                <div class="metric-number">15K +</div>
                                <div class="metric-label">Utilisateurs impactés</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-icon">🎓</div>
                                <div class="metric-number">1 800+</div>
                                <div class="metric-label">Professionnels formés</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-icon">💼</div>
                                <div class="metric-number">100M+</div>
                                <div class="metric-label">Budgets gérés</div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column -->
                    <div class="approach-block">
                        <h3 class="approach-title">Notre approche</h3>
                        <div class="approach-steps">
                            <div class="step-item">
                                <div class="step-icon">🎯</div>
                                <div class="step-content">
                                    <h4>Stratégie Digitale</h4>
                                    <p>Élaboration d'une vision claire et d'une feuille de route pour votre transformation numérique</p>
                                </div>
                            </div>
                            <div class="step-item">
                                <div class="step-icon">⚙️</div>
                                <div class="step-content">
                                    <h4>Solutions sur mesure</h4>
                                    <p>Création d'applications et d'outils personnalisés qui répondent à vos besoins spécifiques</p>
                                </div>
                            </div>
                            <div class="step-item">
                                <div class="step-icon">🤖</div>
                                <div class="step-content">
                                    <h4>Automatisation & IA</h4>
                                    <p>Optimisation de vos processus grâce à l'intelligence artificielle</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Interactive Approach Section -->
                <div class="interactive-approach">
                    <div class="section-header">
                        <!-- <h3 class="section-title">Notre <span class="accent-word">Méthode</span></h3> -->
						<h3 class="approach-title">Notre méthode</h3>
                        <p class="approach-subtitle">L'expertise technologique des grandes entreprises, repensée pour l'agilité des PME. Une vision stratégique sans la bureaucratie corporative.</p>
                    </div>
                    
                    <div class="method-grid">
                        <div class="method-card fade-in" data-modal="external-eye">
                            <span class="card-icon">👁️</span>
                            <h3 class="card-title">Vision Externe</h3>
                            <p class="card-preview">Briser les silos internes et révéler les opportunités invisibles de l'intérieur.</p>
                            <div class="expand-hint">[ Découvrir la philosophie ]</div>
                        </div>
                        
                        <div class="method-card fade-in" data-modal="calculated-progression">
                            <span class="card-icon">🎯</span>
                            <h3 class="card-title">Progression Calculée</h3>
                            <p class="card-preview">Des interventions stratégiques ciblées qui génèrent un impact mesurable rapidement.</p>
                            <div class="expand-hint">[ Comprendre l'approche ]</div>
                        </div>
                        
                        <div class="method-card fade-in" data-modal="strategic-autonomy">
                            <span class="card-icon">🚀</span>
                            <h3 class="card-title">Autonomie Stratégique</h3>
                            <p class="card-preview">Développer votre capacité interne plutôt que créer une dépendance technologique.</p>
                            <div class="expand-hint">[ Voir la différence ]</div>
                        </div>
                        
                        <div class="method-card fade-in" data-modal="enterprise-agility">
                            <span class="card-icon">⚡</span>
                            <h3 class="card-title">Agilité Entreprise</h3>
                            <p class="card-preview">La profondeur stratégique des grandes organisations avec la rapidité d'exécution des PME.</p>
                            <div class="expand-hint">[ Explorer la vision ]</div>
                        </div>
                    </div>
                    
                    <div class="philosophy-section">
                        <h3><center>L'Œil Externe</center></h3>
                        <p class="philosophy-text-approach">
                            Quand vous êtes dans votre business tous les jours, vous ne voyez plus la forêt pour les arbres. 
                            On apporte le regard externe qui vous fait sortir de vos murs et habitudes. 
                            <strong>On pense comme une grande entreprise, mais on agit avec l'agilité d'une PME.</strong>
                        </p>
                    </div>
                    
                    <!-- <div class="cta-section">
                        <button class="cta-button" onclick="openCaseStudy()">
                            ✨ Voir un cas concret
                        </button>
                    </div> -->
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services-section" id="services">
        <div class="section-container">
            <div class="section-header">
                <span class="section-label">02 — Services</span>
                <h2 class="section-title">Nos <span class="accent-word">Services</span></h2>
                <p class="section-subtitle">Des solutions innovantes adaptées à vos défis uniques</p>
            </div>
            
            <div class="services-grid">
                <div class="service-card fade-in">
                    <div class="service-header">
                        <div class="service-icon">🎯</div>
                        <h3 class="service-title">Stratégie Digitale</h3>
                    </div>
                    <p class="service-description">
                        Élaboration d'une vision claire et d'une feuille de route pour votre transformation numérique, en alignement avec vos objectifs d'affaires.
                    </p>
                    <div class="service-features">
                        <span>Audit digital</span>
                        <span>Roadmap stratégique</span>
                        <span>Diagnostic externe</span>
                    </div>
                </div>

				<!-- IMAGE CARD -->
				<div class="service-card image-card fade-in">
					<img src="service1.jpg" alt="Nasa America view night" />
				</div>

                <div class="service-card fade-in">
                    <div class="service-header">
                        <div class="service-icon">⚙️</div>
                        <h3 class="service-title">Développement de Solutions</h3>
                    </div>
                    <p class="service-description">
                        Création d'applications, de plateformes et d'outils personnalisés qui répondent parfaitement à vos besoins spécifiques et optimisent vos opérations.
                    </p>
                    <div class="service-features">
                        <span>Applications web</span>
                        <span>Plateformes métier</span>
                        <span>Intégrations Systemes</span>
                    </div>
                </div>

				<!-- IMAGE CARD -->
				<div class="service-card image-card fade-in">
					<img src="service2.jpg" alt="Code" />
				</div>

                <div class="service-card fade-in">
                    <div class="service-header">
                        <div class="service-icon">🤖</div>
                        <h3 class="service-title">Automatisation & IA</h3>
                    </div>
                    <p class="service-description">
                        Implémentation d'outils d'automatisation et d'intelligence artificielle pour optimiser vos processus et prendre des décisions basées sur les données.
                    </p>
                    <div class="service-features">
                        <span>Automatisation processus</span>
                        <span>IA conversationnelle</span>
                        <span>Optimisation de processus</span>
                    </div>
                </div>

                <div class="service-card fade-in">
                    <div class="service-header">
                        <div class="service-icon">📚</div>
                        <h3 class="service-title">Accompagnement & Formation</h3>
                    </div>
                    <p class="service-description">
                        Support personnalisé et programmes de formation pour assurer une adoption réussie des nouvelles technologies au sein de votre organisation.
                    </p>
                    <div class="service-features">
                        <span>Formation équipes</span>
                        <span>Support technique</span>
                        <span>Gestion du changement</span>
                    </div>
                </div>
				
				<!-- IMAGE CARD -->
				<div class="service-card image-card fade-in">
					<img src="service3.jpg" alt="Placeholder" />
				</div>
				
				<div class="service-card fade-in">
                    <div class="service-header">
                        <div class="service-icon">🔍</div>
                        <h3 class="service-title">Audit & Diagnostic</h3>
                    </div>
                    <p class="service-description">
                        Évaluation approfondie de votre environnement technologique pour identifier les points faibles, réduire les risques et révéler les opportunités d’optimisation.
                    </p>
                    <div class="service-features">
                        <span>Revue Interne</span>
                        <span>Analyse bottlenecks</span>
                        <span>Support technique</span>
                    </div>
                </div>
				
				<!-- IMAGE CARD -->
				<div class="service-card image-card fade-in">
					<img src="service4.jpg" alt="library" />
				</div>
				
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact-section" id="contact">
        <div class="section-container">
            <div class="contact-grid">
                <div class="contact-info">
                    <span class="section-label">03 — Contact</span>
                    <h2 class="section-title">Prêt à <span class="accent-word">transformer</span>?</h2>
                    <p class="contact-description">Prenez rendez-vous pour une consultation gratuite et découvrez comment IMPAXX peut accompagner votre PME dans sa transformation digitale. Commençons par comprendre votre vision - un échange de 30 minutes suffit pour identifier les opportunités d'amélioration qui correspondent à vos objectifs.</p>
                    
                    <div class="contact-details">
                        <div class="detail-item">
                            <span class="detail-label">Email</span>
                            <a href="mailto:<EMAIL>" class="detail-link"><EMAIL></a>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Téléphone</span>
                            <a href="tel:+15148357689" class="detail-link">+****************</a>
                        </div>
                    </div>
                </div>
                
                <div class="contact-form">
                    <form class="consultation-form">
                        <div class="form-row">
                            <div class="form-field">
                                <label for="name">Nom complet</label>
                                <input type="text" id="name" name="name" required>
                            </div>
                            <div class="form-field">
                                <label for="company">Entreprise</label>
                                <input type="text" id="company" name="company" required>
                            </div>
                        </div>
                        <div class="form-field">
                            <label for="email">Email</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        <div class="form-field">
                            <label for="challenge">Votre défi principal</label>
                            <textarea id="challenge" name="challenge" rows="4" placeholder="Décrivez brièvement le défi ou l'opportunité que vous souhaitez explorer..."></textarea>
                        </div>
                        <button type="submit" class="submit-btn">Planifier une consultation</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-links">
                <a href="/legal#privacy">Politique de confidentialité</a>
                <a href="/legal#terms">Conditions d'utilisation</a>
                <a href="/legal#support">Support</a>
            </div>
            <div class="copyright">
                © 2025 IMPAXX. Tous droits réservés.
            </div>
        </div>
    </footer>

    <!-- Modals for Interactive Approach -->
    <div id="external-eye" class="modal">
        <div class="modal-content">
            <button class="modal-close" onclick="closeModal()">&times;</button>
            <h3>👁️ Vision Externe</h3>
            <p>Les organisations développent naturellement des angles morts. Les processus internes, les habitudes départementales et les contraintes historiques créent un tunnel de vision qui limite l'innovation.</p>
            <p><strong>Notre expertise</strong> réside dans l'analyse objective de vos opérations depuis une perspective externe. Cette approche révèle les <span class="highlight">opportunités stratégiques</span> invisibles de l'intérieur.</p>
            <p>Nous appliquons les méthodologies éprouvées des grandes entreprises tout en conservant l'agilité décisionnelle qui caractérise les PME performantes.</p>
        </div>
    </div>
    
    <div id="calculated-progression" class="modal">
        <div class="modal-content">
            <button class="modal-close" onclick="closeModal()">&times;</button>
            <h3>🎯 Progression Calculée</h3>
            <p>Les transformations technologiques massives échouent statistiquement. Notre approche privilégie les <span class="highlight">interventions stratégiques ciblées</span> qui génèrent un retour sur investissement mesurable rapidement.</p>
            <p><strong>Méthodologie :</strong></p>
            <p>• Identification des leviers à fort impact<br>• Déploiement par phases avec validation continue<br>• Construction sur les succès pour maintenir l'élan<br>• Intégration progressive dans vos processus existants</p>
            <p>Cette approche minimise les risques tout en maximisant l'adoption organisationnelle des nouvelles solutions.</p>
        </div>
    </div>
    
    <div id="strategic-autonomy" class="modal">
        <div class="modal-content">
            <button class="modal-close" onclick="closeModal()">&times;</button>
            <h3>🚀 Autonomie Stratégique</h3>
            <p>Notre mission est de <span class="highlight">développer votre capacité interne</span> plutôt que de créer une dépendance technologique ou consultative.</p>
            <p>Chaque intervention inclut un transfert de connaissances structuré qui permet à vos équipes de maintenir et faire évoluer les solutions implémentées.</p>
            <p><strong>Principe fondamental :</strong> Votre succès se mesure à votre indépendance progressive. Nous concevons des solutions qui grandissent avec votre organisation sans créer de contraintes techniques ou contractuelles.</p>
            <p>Cette philosophie distingue fondamentalement IMPAXX des modèles traditionnels de consulting technologique.</p>
        </div>
    </div>
    
    <div id="enterprise-agility" class="modal">
        <div class="modal-content">
            <button class="modal-close" onclick="closeModal()">&times;</button>
            <h3>⚡ Agilité Entreprise</h3>
            <p>Les grandes organisations excellent dans la planification stratégique mais souffrent de lourdeur d'exécution. Les PME ont l'avantage de la rapidité mais manquent souvent de vision systémique.</p>
            <p><strong>Notre valeur unique :</strong> Nous combinons la <span class="highlight">profondeur analytique des grandes entreprises</span> avec la <span class="highlight">vitesse d'exécution des PME</span>.</p>
            <p>Cette synthèse permet d'éviter les écueils de la sur-ingénierie tout en maintenant la rigueur stratégique nécessaire à des solutions durables.</p>
            <p>Résultat : Des projets qui démarrent rapidement, évoluent intelligemment, et s'intègrent naturellement dans votre écosystème technologique.</p>
        </div>
    </div>
    
    <div id="case-study" class="modal">
        <div class="modal-content">
            <button class="modal-close" onclick="closeModal()">&times;</button>
            <h3>📖 Cas Concret : Le Site Web de 2 Ans</h3>
            <p><strong>Leur problème (supposé) :</strong> "On a embauché des designers web mais on n'a pas le temps de leur dire quoi faire. On a des graphistes mais on n'est pas sûrs pour la nouvelle identité..."</p>
            <p><strong>Notre diagnostic :</strong> Ils n'avaient pas besoin de plus de "faiseurs". Ils avaient besoin de <span class="highlight">coordination et de stratégie</span>.</p>
            <p><strong>Le vrai problème :</strong> Le bandwidth de leadership, pas les mises à jour du site web.</p>
            <p><strong>La solution :</strong> Au lieu d'ajouter des ressources, on a créé un processus de coordination qui a débloqué tout en 6 semaines.</p>
            <p><strong>Résultat :</strong> Site lancé, équipe alignée, et ils ont économisé des mois de frustration.</p>
        </div>
    </div>

    <script>
        // Interactive cursor with controlled movement
        const cursor = document.querySelector('.cursor');
        const nodes = document.querySelectorAll('.neural-node');
        
        let mouseX = 0, mouseY = 0;
        let cursorX = 0, cursorY = 0;
        
        // Smooth cursor movement
        document.addEventListener('mousemove', (e) => {
            mouseX = e.clientX;
            mouseY = e.clientY;
        });
        
        // Animate cursor with controlled speed
        function updateCursor() {
            // Smooth interpolation for cursor movement
            cursorX += (mouseX - cursorX) * 0.1;
            cursorY += (mouseY - cursorY) * 0.1;
            
            cursor.style.left = cursorX + 'px';
            cursor.style.top = cursorY + 'px';
            
            // Reactive nodes with distance-based effects
            nodes.forEach(node => {
                const rect = node.getBoundingClientRect();
                const distance = Math.sqrt(
                    Math.pow(cursorX - (rect.left + rect.width/2), 2) + 
                    Math.pow(cursorY - (rect.top + rect.height/2), 2)
                );
                
                if (distance < 120) {
                    const intensity = (120 - distance) / 120;
                    node.style.transform = `scale(${1 + intensity * 1.8})`;
                    node.style.opacity = 0.7 + intensity * 0.3;
                    node.style.boxShadow = `0 0 ${15 * intensity}px rgba(168, 162, 158, ${0.4 * intensity})`;
                } else {
                    node.style.transform = 'scale(1)';
                    node.style.opacity = '0.7';
                    node.style.boxShadow = '0 0 8px rgba(168, 162, 158, 0.3)';
                }
            });
            
            requestAnimationFrame(updateCursor);
        }
        
        updateCursor();
        
        // Add click effects
        document.addEventListener('mousedown', () => {
            cursor.classList.add('active');
        });
        
        document.addEventListener('mouseup', () => {
            cursor.classList.remove('active');
        });

        // Modal Functions
        function openModal(modalId) {
            document.getElementById(modalId).classList.add('active');
            document.body.style.overflow = 'hidden';
        }
        
        function closeModal() {
            document.querySelectorAll('.modal').forEach(modal => {
                modal.classList.remove('active');
            });
            document.body.style.overflow = 'auto';
        }
        
        function openCaseStudy() {
            openModal('case-study');
        }
        
        // Add click handlers to method cards
        document.querySelectorAll('.method-card').forEach(card => {
            card.addEventListener('click', () => {
                const modalId = card.getAttribute('data-modal');
                openModal(modalId);
            });
        });
        
        // Close modal on outside click
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeModal();
                }
            });
        });
        
        // Close on Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeModal();
            }
        });

        // Smooth scrolling for navigation
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Navbar scroll effect
        let lastScrollTop = 0;
        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const nav = document.querySelector('.nav'); 
            
            if (scrollTop > 50) {
                nav.classList.add('scrolled');
            } else {
                nav.classList.remove('scrolled');
            }
            
            lastScrollTop = scrollTop;
        });

        // Intersection Observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        // Observe fade-in elements
        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });

        // Mobile menu (basic functionality)
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const navLinks = document.querySelector('.nav-links');
        
        if (mobileMenuBtn && navLinks) {
            mobileMenuBtn.addEventListener('click', () => {
                navLinks.style.display = navLinks.style.display === 'flex' ? 'none' : 'flex';
            });
        }
    </script>
</body>
</html>